#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
技术指标组合分析程序
分析五个技术指标筛选条件的所有可能组合，计算各种统计指标
"""

import pandas as pd
import numpy as np
from itertools import combinations
import warnings
warnings.filterwarnings('ignore')

class TechnicalIndicatorAnalyzer:
    """技术指标组合分析器"""
    
    def __init__(self, data_file_path):
        """
        初始化分析器
        
        Args:
            data_file_path (str): CSV数据文件路径
        """
        self.data_file_path = data_file_path
        self.data = None
        self.required_columns = [
            'i1_MA20_ratio',
            'i1_最高_收盘比例', 
            'i1_EMA10_ratio',
            'i1_EMA5_ratio',
            'i1_EMA20_ratio'
        ]
        self.profit_column = '成本后收益率'  # 使用成本后收益率作为目标变量
        
        # 筛选条件（基于85%阈值的最佳条件）
        self.filter_conditions = {
            'i1_MA20_ratio': 1.005,
            'i1_最高_收盘比例': 0.058,
            'i1_EMA10_ratio': 1.013,
            'i1_EMA5_ratio': 1.026,
            'i1_EMA20_ratio': 1.007
        }
    
    def load_data(self):
        """
        加载CSV数据并验证必需列
        
        Returns:
            bool: 加载成功返回True，失败返回False
        """
        try:
            print("正在加载数据文件...")
            self.data = pd.read_csv(self.data_file_path)
            print(f"数据加载成功，共 {len(self.data)} 行，{len(self.data.columns)} 列")
            
            # 验证必需列是否存在
            missing_columns = []
            for col in self.required_columns + [self.profit_column]:
                if col not in self.data.columns:
                    missing_columns.append(col)
            
            if missing_columns:
                print(f"错误：缺少必需的列: {missing_columns}")
                return False
            
            print("所有必需列验证通过")
            
            # 数据类型转换和清理
            for col in self.required_columns:
                self.data[col] = pd.to_numeric(self.data[col], errors='coerce')
            
            self.data[self.profit_column] = pd.to_numeric(self.data[self.profit_column], errors='coerce')
            
            # 删除包含NaN值的行
            initial_rows = len(self.data)
            self.data = self.data.dropna(subset=self.required_columns + [self.profit_column])
            final_rows = len(self.data)
            
            if initial_rows != final_rows:
                print(f"清理数据：删除了 {initial_rows - final_rows} 行包含缺失值的数据")
            
            return True
            
        except FileNotFoundError:
            print(f"错误：找不到数据文件 {self.data_file_path}")
            return False
        except Exception as e:
            print(f"数据加载失败：{str(e)}")
            return False
    
    def validate_filter_conditions(self):
        """
        验证筛选条件的有效性
        
        Returns:
            bool: 验证通过返回True，失败返回False
        """
        try:
            print("验证筛选条件...")
            
            for indicator, threshold in self.filter_conditions.items():
                if indicator not in self.required_columns:
                    print(f"错误：筛选条件中的指标 {indicator} 不在必需列中")
                    return False
                
                # 检查阈值是否合理
                data_min = self.data[indicator].min()
                data_max = self.data[indicator].max()
                
                print(f"{indicator}: 阈值={threshold}, 数据范围=[{data_min:.6f}, {data_max:.6f}]")
                
                if not (data_min <= threshold <= data_max):
                    print(f"警告：{indicator} 的阈值 {threshold} 超出数据范围")
            
            print("筛选条件验证完成")
            return True
            
        except Exception as e:
            print(f"筛选条件验证失败：{str(e)}")
            return False
    
    def generate_combinations(self):
        """
        生成所有可能的指标组合
        
        Returns:
            dict: 按组合长度分组的所有组合
        """
        indicators = list(self.filter_conditions.keys())
        all_combinations = {}
        
        for r in range(1, len(indicators) + 1):
            combo_list = list(combinations(indicators, r))
            all_combinations[r] = combo_list
            print(f"{r}重组合: {len(combo_list)} 种")
        
        total_combinations = sum(len(combos) for combos in all_combinations.values())
        print(f"总组合数: {total_combinations}")
        
        return all_combinations
    
    def analyze_single_combination(self, combination):
        """
        分析单个指标组合

        Args:
            combination (tuple): 指标组合

        Returns:
            dict: 分析结果
        """
        try:
            # 构建筛选条件
            filter_mask = pd.Series([True] * len(self.data))

            for indicator in combination:
                threshold = self.filter_conditions[indicator]
                if '比例' in indicator:
                    # 对于比例类指标，使用大于等于条件
                    filter_mask &= (self.data[indicator] >= threshold)
                else:
                    # 对于ratio类指标，使用大于等于条件
                    filter_mask &= (self.data[indicator] >= threshold)

            # 筛选数据
            filtered_data = self.data[filter_mask]
            sample_count = len(filtered_data)
            total_samples = len(self.data)

            if sample_count == 0:
                return {
                    'combination': combination,
                    'combination_str': ' + '.join(combination),
                    'filtered_sample_count': 0,
                    'sample_ratio': 0,
                    'win_rate_gt0': 0,
                    'win_rate_gt1': 0,
                    'win_rate_gt3': 0,
                    'avg_return': 0,
                    'median_return': 0,
                    'return_std': 0,
                    'max_return': 0,
                    'min_return': 0,
                    'positive_count': 0
                }

            # 计算收益率统计
            returns = filtered_data[self.profit_column]

            # 计算各种胜率
            win_gt0 = (returns > 0).sum()
            win_gt1 = (returns > 1).sum()
            win_gt3 = (returns > 3).sum()

            win_rate_gt0 = win_gt0 / sample_count * 100
            win_rate_gt1 = win_gt1 / sample_count * 100
            win_rate_gt3 = win_gt3 / sample_count * 100

            # 计算样本占比
            sample_ratio = sample_count / total_samples * 100

            result = {
                'combination': combination,
                'combination_str': ' + '.join(combination),
                'filtered_sample_count': sample_count,
                'sample_ratio': sample_ratio,
                'win_rate_gt0': win_rate_gt0,
                'win_rate_gt1': win_rate_gt1,
                'win_rate_gt3': win_rate_gt3,
                'avg_return': returns.mean(),
                'median_return': returns.median(),
                'return_std': returns.std(),
                'max_return': returns.max(),
                'min_return': returns.min(),
                'positive_count': win_gt0
            }

            return result

        except Exception as e:
            print(f"分析组合 {combination} 时出错：{str(e)}")
            return None
    
    def batch_analyze_combinations(self, combinations_dict):
        """
        批量分析所有组合
        
        Args:
            combinations_dict (dict): 所有组合字典
            
        Returns:
            list: 所有分析结果
        """
        all_results = []
        total_combinations = sum(len(combos) for combos in combinations_dict.values())
        current_count = 0
        
        print("开始批量分析...")
        
        for combo_length, combinations_list in combinations_dict.items():
            print(f"\n分析 {combo_length} 重组合...")
            
            for combination in combinations_list:
                current_count += 1
                result = self.analyze_single_combination(combination)
                
                if result is not None:
                    result['combination_length'] = combo_length
                    all_results.append(result)
                
                # 显示进度
                if current_count % 10 == 0 or current_count == total_combinations:
                    progress = current_count / total_combinations * 100
                    print(f"进度: {current_count}/{total_combinations} ({progress:.1f}%)")
        
        print(f"批量分析完成，共分析 {len(all_results)} 个组合")
        return all_results
    
    def format_results(self, results):
        """
        格式化分析结果为DataFrame

        Args:
            results (list): 分析结果列表

        Returns:
            pd.DataFrame: 格式化后的结果表格
        """
        if not results:
            print("没有分析结果可以格式化")
            return pd.DataFrame()

        # 转换为DataFrame
        df_results = pd.DataFrame(results)

        # 重新排列列顺序
        column_order = [
            'combination_length', 'combination_str', 'filtered_sample_count',
            'sample_ratio', 'win_rate_gt0', 'win_rate_gt1', 'win_rate_gt3',
            'avg_return', 'median_return', 'return_std',
            'max_return', 'min_return'
        ]

        df_results = df_results[column_order]

        # 重命名列为中文
        df_results.columns = [
            '组合复杂度', '指标组合', '筛选后样本数',
            '样本占比(%)', '胜率(>0%)(%)', '胜率(>1%)(%)', '胜率(>3%)(%)',
            '平均收益', '中位数收益', '收益标准差',
            '最大收益', '最小收益'
        ]

        # 按组合复杂度和胜率(>0%)排序
        df_results = df_results.sort_values(['组合复杂度', '胜率(>0%)(%)'], ascending=[True, False])

        # 格式化数值显示
        df_results['样本占比(%)'] = df_results['样本占比(%)'].round(2)
        df_results['胜率(>0%)(%)'] = df_results['胜率(>0%)(%)'].round(2)
        df_results['胜率(>1%)(%)'] = df_results['胜率(>1%)(%)'].round(2)
        df_results['胜率(>3%)(%)'] = df_results['胜率(>3%)(%)'].round(2)
        df_results['平均收益'] = df_results['平均收益'].round(4)
        df_results['中位数收益'] = df_results['中位数收益'].round(4)
        df_results['收益标准差'] = df_results['收益标准差'].round(4)
        df_results['最大收益'] = df_results['最大收益'].round(4)
        df_results['最小收益'] = df_results['最小收益'].round(4)

        return df_results
    
    def run_analysis(self):
        """
        运行完整的分析流程
        
        Returns:
            pd.DataFrame: 分析结果表格
        """
        print("=" * 60)
        print("技术指标组合分析程序")
        print("=" * 60)
        
        # 1. 加载数据
        if not self.load_data():
            return None
        
        # 2. 验证筛选条件
        if not self.validate_filter_conditions():
            return None
        
        # 3. 生成组合
        print("\n生成指标组合...")
        combinations_dict = self.generate_combinations()
        
        # 4. 批量分析
        results = self.batch_analyze_combinations(combinations_dict)
        
        # 5. 格式化结果
        print("\n格式化分析结果...")
        df_results = self.format_results(results)
        
        return df_results


def main():
    """主函数"""
    # 数据文件路径
    data_file = r"c:\Zhou\coding\gupiao\fenxi\15di_fantan_2019110120250622.csv"
    
    # 创建分析器实例
    analyzer = TechnicalIndicatorAnalyzer(data_file)
    
    # 运行分析
    results_df = analyzer.run_analysis()
    
    if results_df is not None and not results_df.empty:
        print("\n" + "=" * 80)
        print("分析结果汇总")
        print("=" * 80)
        
        # 按组合复杂度分组显示结果
        for complexity in sorted(results_df['组合复杂度'].unique()):
            group_data = results_df[results_df['组合复杂度'] == complexity]
            print(f"\n{complexity}重组合分析结果 (共{len(group_data)}种组合):")
            print("-" * 80)
            print(group_data.to_string(index=False))
        
        # 保存结果到CSV文件
        output_file = "技术指标组合分析结果.csv"
        results_df.to_csv(output_file, index=False, encoding='utf-8-sig')
        print(f"\n分析结果已保存到: {output_file}")
        
        # 显示最佳组合
        print("\n" + "=" * 80)
        print("最佳组合推荐 (按成功率排序)")
        print("=" * 80)
        
        for complexity in sorted(results_df['组合复杂度'].unique()):
            group_data = results_df[results_df['组合复杂度'] == complexity]
            if not group_data.empty:
                best_combo = group_data.iloc[0]
                print(f"{complexity}重组合最佳: {best_combo['指标组合']} "
                      f"(胜率(>0%): {best_combo['胜率(>0%)(%)']:.2f}%, "
                      f"样本数: {best_combo['筛选后样本数']}, "
                      f"平均收益: {best_combo['平均收益']:.4f})")
    
    print("\n程序执行完成！")


if __name__ == "__main__":
    main()
