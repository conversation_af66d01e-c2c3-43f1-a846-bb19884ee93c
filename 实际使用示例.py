#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
predict_stock_screening 函数实际使用示例
展示如何在实际投资项目中使用股票筛选预测函数
"""

import pandas as pd
from 筛选策略分析程序 import predict_stock_screening

def daily_stock_screening():
    """日常股票筛选示例"""
    print("📈 日常股票筛选流程")
    print("="*40)
    
    # 1. 加载最新股票数据
    print("1️⃣ 加载股票数据...")
    try:
        stock_data = pd.read_csv("15di_fantan_2024110120250621.csv", encoding='utf-8')
        print(f"   ✅ 成功加载 {len(stock_data)} 只股票数据")
    except Exception as e:
        print(f"   ❌ 数据加载失败：{e}")
        return
    
    # 2. 执行筛选
    print("\n2️⃣ 执行技术指标筛选...")
    detailed_results = predict_stock_screening(stock_data, return_details=True)
    
    # 3. 分析筛选结果
    print(f"\n3️⃣ 筛选结果分析：")
    print(f"   📊 总股票数：{detailed_results['total_stocks']}")
    print(f"   🎯 通过筛选：{detailed_results['selected_count']} 只")
    print(f"   📈 筛选通过率：{detailed_results['selection_rate']:.2%}")
    print(f"   💰 预期平均收益：{detailed_results['expected_performance']['average_return']:.2f}%")
    print(f"   🏆 预期胜率：{detailed_results['expected_performance']['win_rate']:.1f}%")
    
    # 4. 生成投资建议
    if detailed_results['selected_count'] > 0:
        print(f"\n4️⃣ 投资建议：")
        selected_stocks = detailed_results['selected_stocks']
        
        # 按预期收益排序
        if '3H_2buy' in selected_stocks.columns:
            selected_stocks = selected_stocks.sort_values('3H_2buy', ascending=False)
        
        print(f"   🌟 推荐股票组合（按预期收益排序）：")
        for i, (idx, row) in enumerate(selected_stocks.iterrows(), 1):
            stock_id = row['secID']
            stock_name = row.get('secShortName', 'N/A')
            expected_return = row.get('3H_2buy', 'N/A')
            print(f"      {i}. {stock_id} ({stock_name}) - 预期收益: {expected_return}%")
        
        # 投资配置建议
        stock_count = detailed_results['selected_count']
        equal_weight = 100 / stock_count
        print(f"\n   💼 配置建议：")
        print(f"      • 股票数量：{stock_count} 只")
        print(f"      • 等权重配置：每只 {equal_weight:.1f}%")
        print(f"      • 建议股票仓位：80-85%")
        print(f"      • 建议现金比例：15-20%")
        
        return selected_stocks
    else:
        print(f"\n4️⃣ ⚠️  当前没有股票通过筛选，建议等待更好的市场时机")
        return None

def portfolio_management_example():
    """投资组合管理示例"""
    print(f"\n💼 投资组合管理示例")
    print("="*40)
    
    # 执行筛选
    stock_data = pd.read_csv("15di_fantan_2024110120250621.csv", encoding='utf-8')
    results = predict_stock_screening(stock_data, return_details=True)
    
    if results['selected_count'] > 0:
        selected_stocks = results['selected_stocks']
        
        # 风险评估
        print("1️⃣ 风险评估：")
        if '3H_2buy' in selected_stocks.columns:
            returns = selected_stocks['3H_2buy']
            risk_metrics = {
                'max_return': returns.max(),
                'min_return': returns.min(),
                'volatility': returns.std(),
                'downside_risk': returns[returns < 0].count() / len(returns) * 100
            }
            
            print(f"   📈 最高预期收益：{risk_metrics['max_return']:.2f}%")
            print(f"   📉 最低预期收益：{risk_metrics['min_return']:.2f}%")
            print(f"   📊 收益波动率：{risk_metrics['volatility']:.2f}%")
            print(f"   ⚠️  下行风险：{risk_metrics['downside_risk']:.1f}%")
        
        # 资金分配建议
        print(f"\n2️⃣ 资金分配建议（假设总资金100万）：")
        total_capital = 1000000  # 100万
        stock_position = 0.8  # 80%股票仓位
        stock_capital = total_capital * stock_position
        per_stock_capital = stock_capital / len(selected_stocks)
        
        print(f"   💰 总资金：{total_capital:,} 元")
        print(f"   📈 股票仓位：{stock_capital:,} 元 ({stock_position:.0%})")
        print(f"   💵 现金仓位：{total_capital - stock_capital:,} 元 ({1-stock_position:.0%})")
        print(f"   🎯 每只股票配置：{per_stock_capital:,.0f} 元")
        
        # 具体买入建议
        print(f"\n3️⃣ 具体买入建议：")
        for i, (idx, row) in enumerate(selected_stocks.iterrows(), 1):
            stock_id = row['secID']
            stock_name = row.get('secShortName', 'N/A')
            print(f"   {i}. {stock_id} ({stock_name})：{per_stock_capital:,.0f} 元")
        
        # 风险管理提醒
        print(f"\n4️⃣ 风险管理提醒：")
        print(f"   🛡️  单只股票止损：-5%")
        print(f"   🛡️  组合整体止损：-8%")
        print(f"   🔄 重新筛选频率：每周一次")
        print(f"   📊 业绩评估频率：每月一次")

def batch_screening_example():
    """批量筛选示例"""
    print(f"\n🔄 批量筛选示例")
    print("="*40)
    
    # 模拟多个数据文件的批量处理
    data_files = ["15di_fantan_2024110120250621.csv"]  # 实际使用时可以是多个文件
    
    all_results = []
    
    for i, file_path in enumerate(data_files, 1):
        print(f"{i}️⃣ 处理文件：{file_path}")
        
        try:
            # 加载数据
            data = pd.read_csv(file_path, encoding='utf-8')
            
            # 执行筛选
            results = predict_stock_screening(data, return_details=True)
            
            # 记录结果
            file_result = {
                'file': file_path,
                'total_stocks': results['total_stocks'],
                'selected_count': results['selected_count'],
                'selection_rate': results['selection_rate'],
                'selected_stocks': results['selected_stocks']
            }
            all_results.append(file_result)
            
            print(f"   ✅ {results['selected_count']} 只股票通过筛选 ({results['selection_rate']:.2%})")
            
        except Exception as e:
            print(f"   ❌ 处理失败：{e}")
    
    # 汇总结果
    if all_results:
        print(f"\n📊 批量筛选汇总：")
        total_stocks = sum(r['total_stocks'] for r in all_results)
        total_selected = sum(r['selected_count'] for r in all_results)
        overall_rate = total_selected / total_stocks if total_stocks > 0 else 0
        
        print(f"   📈 总处理股票：{total_stocks} 只")
        print(f"   🎯 总筛选通过：{total_selected} 只")
        print(f"   📊 整体通过率：{overall_rate:.2%}")

def main():
    """主函数"""
    print("🚀 predict_stock_screening 函数实际使用示例")
    print("展示如何在实际投资项目中使用股票筛选预测函数")
    print()
    
    try:
        # 1. 日常筛选流程
        selected_stocks = daily_stock_screening()
        
        # 2. 投资组合管理
        if selected_stocks is not None:
            portfolio_management_example()
        
        # 3. 批量处理示例
        batch_screening_example()
        
        print(f"\n🎉 示例演示完成！")
        print(f"💡 您现在可以将 predict_stock_screening 函数集成到您的投资系统中。")
        
    except Exception as e:
        print(f"❌ 示例执行出错：{e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
