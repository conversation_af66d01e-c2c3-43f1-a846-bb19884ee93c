#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试筛选预测函数的使用示例
演示predict_stock_screening函数的各种用法
"""

import pandas as pd
import numpy as np
from 筛选策略分析程序 import predict_stock_screening

def test_predict_function():
    """测试predict_stock_screening函数"""
    print("🧪 测试股票筛选预测函数")
    print("="*50)
    
    # 加载真实数据
    try:
        print("📊 加载真实股票数据...")
        stock_data = pd.read_csv("15di_fantan_2024110120250621.csv", encoding='utf-8')
        print(f"✅ 成功加载数据：{len(stock_data)} 只股票")
        
        # 测试1：简单使用
        print(f"\n🔍 测试1：简单筛选（返回布尔值列表）")
        results = predict_stock_screening(stock_data)
        passed_count = sum(results)
        print(f"  通过筛选的股票数量：{passed_count} 只")
        print(f"  筛选通过率：{passed_count/len(stock_data)*100:.2f}%")
        
        # 测试2：详细使用
        print(f"\n📈 测试2：详细筛选（返回完整信息）")
        detailed_results = predict_stock_screening(stock_data, return_details=True)
        
        print(f"  总股票数：{detailed_results['total_stocks']}")
        print(f"  通过筛选：{detailed_results['selected_count']} 只")
        print(f"  筛选通过率：{detailed_results['selection_rate']:.2%}")
        print(f"  预期平均收益：{detailed_results['expected_performance']['average_return']:.2f}%")
        print(f"  预期胜率：{detailed_results['expected_performance']['win_rate']:.1f}%")
        
        # 显示筛选条件
        print(f"\n⚙️  使用的筛选条件：")
        for indicator, threshold in detailed_results['screening_conditions'].items():
            individual_pass = detailed_results['screening_summary']['individual_pass_rates'][indicator]
            print(f"    {indicator} ≥ {threshold:.4f} ({individual_pass} 只股票通过)")
        
        # 显示筛选出的股票
        if detailed_results['selected_count'] > 0:
            print(f"\n🎯 筛选出的优质股票：")
            selected_stocks = detailed_results['selected_stocks']
            
            # 如果有股票代码列，显示具体信息
            if 'secID' in selected_stocks.columns:
                for i, (idx, row) in enumerate(selected_stocks.iterrows(), 1):
                    stock_id = row['secID']
                    stock_name = row.get('secShortName', 'N/A')
                    expected_return = row.get('3H_2buy', 'N/A')
                    print(f"    {i}. {stock_id} ({stock_name}) - 预期收益: {expected_return}%")
            
            # 投资组合建议
            print(f"\n💼 投资组合建议：")
            stock_count = detailed_results['selected_count']
            equal_weight = 100 / stock_count
            print(f"    建议配置：{stock_count} 只股票，每只 {equal_weight:.1f}%")
            print(f"    股票仓位：80-85%，现金比例：15-20%")
        
        # 测试3：错误处理
        print(f"\n❌ 测试3：错误处理测试")
        
        # 创建缺少技术指标的测试数据
        incomplete_data = pd.DataFrame({
            'secID': ['TEST001', 'TEST002'],
            'i1_MA20_ratio': [1.01, 1.02],
            # 故意缺少其他技术指标
        })
        
        try:
            # 简单模式下应该抛出异常
            predict_stock_screening(incomplete_data)
        except ValueError as e:
            print(f"  ✅ 简单模式正确抛出异常：{e}")
        
        # 详细模式下应该返回错误信息
        error_results = predict_stock_screening(incomplete_data, return_details=True)
        if 'error' in error_results:
            print(f"  ✅ 详细模式正确返回错误信息：{error_results['error']}")
            print(f"  缺失的指标：{error_results['missing_indicators']}")
        
        # 测试4：性能测试
        print(f"\n⚡ 测试4：性能测试")
        import time
        
        start_time = time.time()
        for _ in range(10):
            predict_stock_screening(stock_data)
        end_time = time.time()
        
        avg_time = (end_time - start_time) / 10
        print(f"  平均执行时间：{avg_time*1000:.2f} 毫秒")
        print(f"  处理速度：{len(stock_data)/avg_time:.0f} 只股票/秒")
        
        print(f"\n✅ 所有测试完成！函数工作正常。")
        
    except FileNotFoundError:
        print("❌ 找不到数据文件，创建模拟数据进行测试...")
        test_with_mock_data()
    except Exception as e:
        print(f"❌ 测试过程中出现错误：{e}")
        import traceback
        traceback.print_exc()

def test_with_mock_data():
    """使用模拟数据测试函数"""
    print(f"\n🎭 使用模拟数据测试...")
    
    # 创建模拟股票数据
    np.random.seed(42)
    n_stocks = 100
    
    mock_data = pd.DataFrame({
        'secID': [f'MOCK{i:03d}' for i in range(n_stocks)],
        'secShortName': [f'模拟股票{i}' for i in range(n_stocks)],
        'i1_MA20_ratio': np.random.normal(1.0, 0.02, n_stocks),
        'i1_最高_收盘比例': np.random.uniform(0.02, 0.15, n_stocks),
        'i1_EMA10_ratio': np.random.normal(1.0, 0.025, n_stocks),
        'i1_EMA5_ratio': np.random.normal(1.0, 0.03, n_stocks),
        'i1_EMA20_ratio': np.random.normal(1.0, 0.015, n_stocks),
        '3H_2buy': np.random.normal(3.0, 5.0, n_stocks)
    })
    
    print(f"✅ 创建了 {len(mock_data)} 只模拟股票的数据")
    
    # 测试筛选功能
    detailed_results = predict_stock_screening(mock_data, return_details=True)
    
    print(f"\n📊 模拟数据筛选结果：")
    print(f"  通过筛选：{detailed_results['selected_count']} 只")
    print(f"  筛选通过率：{detailed_results['selection_rate']:.2%}")
    
    if detailed_results['selected_count'] > 0:
        print(f"\n🎯 筛选出的模拟股票：")
        for i, (idx, row) in enumerate(detailed_results['selected_stocks'].iterrows(), 1):
            print(f"    {i}. {row['secID']} - 模拟收益: {row['3H_2buy']:.2f}%")

def demonstrate_usage_examples():
    """演示函数的各种使用方式"""
    print(f"\n📚 函数使用示例演示")
    print("="*50)
    
    # 示例代码（注释形式）
    example_code = '''
# 示例1：基本使用
import pandas as pd
from 筛选策略分析程序 import predict_stock_screening

# 加载股票数据
stock_data = pd.read_csv('your_stock_data.csv')

# 简单筛选
results = predict_stock_screening(stock_data)
print(f"通过筛选的股票数量: {sum(results)}")

# 示例2：获取详细信息
detailed_results = predict_stock_screening(stock_data, return_details=True)
print(f"筛选通过率: {detailed_results['selection_rate']:.2%}")

# 获取筛选出的股票
selected_stocks = detailed_results['selected_stocks']
print("推荐股票列表:")
for idx, row in selected_stocks.iterrows():
    print(f"  {row['secID']}: 预期收益 {row.get('3H_2buy', 'N/A')}%")

# 示例3：投资组合构建
if detailed_results['selected_count'] > 0:
    stock_count = detailed_results['selected_count']
    equal_weight = 100 / stock_count
    print(f"建议等权重配置：每只股票 {equal_weight:.1f}%")

# 示例4：错误处理
try:
    results = predict_stock_screening(incomplete_data)
except ValueError as e:
    print(f"数据验证失败: {e}")

# 示例5：批量处理
for data_file in ['data1.csv', 'data2.csv', 'data3.csv']:
    data = pd.read_csv(data_file)
    results = predict_stock_screening(data, return_details=True)
    print(f"{data_file}: {results['selected_count']} 只股票通过筛选")
    '''
    
    print("💡 使用示例代码：")
    print(example_code)

def main():
    """主函数"""
    print("股票筛选预测函数测试程序")
    print("测试 predict_stock_screening 函数的功能和性能")
    print()
    
    # 运行测试
    test_predict_function()
    
    # 演示使用示例
    demonstrate_usage_examples()
    
    print(f"\n🎉 测试程序完成！")
    print(f"predict_stock_screening 函数已准备就绪，可以用于实际股票筛选。")

if __name__ == "__main__":
    main()
