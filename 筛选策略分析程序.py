#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
股票技术指标筛选策略分析程序
基于前期分析结果的最佳技术指标进行筛选策略对比
分析文件：15di_fantan_2024110120250621.csv
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple
import warnings
warnings.filterwarnings('ignore')

class StockScreeningAnalyzer:
    """股票筛选策略分析器"""
    
    def __init__(self, csv_file_path: str):
        """
        初始化分析器
        
        Args:
            csv_file_path: CSV文件路径
        """
        self.csv_file_path = csv_file_path
        self.data = None
        self.target_column = '3H_2buy'
        
        # 基于前期分析结果的前5名最佳技术指标
        self.key_indicators = [
            'i1_MA20_ratio',      # 排名第1，综合评分3.393
            'i1_最高_收盘比例',          # 排名第2，综合评分3.183
            'i1_EMA10_ratio',     # 排名第3，综合评分2.963
            'i1_EMA5_ratio',      # 排名第4，综合评分2.830
            'i1_EMA20_ratio'      # 排名第5，综合评分2.828
        ]
        
        # 基于前期分析的最佳阈值（95%百分位数对应的具体数值）
        self.best_thresholds = {
            'i1_MA20_ratio': 1.0068,
            'i1_最高_收盘比例': 0.0645,
            'i1_EMA10_ratio': 1.0203,
            'i1_EMA5_ratio': 1.0345,
            'i1_EMA20_ratio': 1.0102
        }
        
        # 筛选策略定义
        self.screening_strategies = {
            '严格筛选': {'percentile': 95, 'description': '95%百分位数阈值'},
            '中等筛选': {'percentile': 90, 'description': '90%百分位数阈值'},
            '放宽筛选': {'percentile': 85, 'description': '85%百分位数阈值'}
        }
        
        # 存储分析结果
        self.analysis_results = {}
        
    def load_and_preprocess_data(self) -> None:
        """加载和预处理数据"""
        print("正在加载数据...")
        print(f"文件路径：{self.csv_file_path}")
        
        try:
            # 尝试UTF-8编码
            self.data = pd.read_csv(self.csv_file_path, encoding='utf-8')
            print(f"成功加载数据（UTF-8编码），共 {len(self.data)} 行，{len(self.data.columns)} 列")
        except UnicodeDecodeError:
            try:
                # 尝试GBK编码
                self.data = pd.read_csv(self.csv_file_path, encoding='gbk')
                print(f"成功加载数据（GBK编码），共 {len(self.data)} 行，{len(self.data.columns)} 列")
            except Exception as e:
                raise Exception(f"无法加载CSV文件：{e}")
        
        # 检查目标列是否存在
        if self.target_column not in self.data.columns:
            raise ValueError(f"目标列 '{self.target_column}' 不存在于数据中")
        
        # 检查关键技术指标列是否存在
        missing_indicators = [col for col in self.key_indicators if col not in self.data.columns]
        if missing_indicators:
            print(f"警告：以下关键技术指标列不存在于数据中：{missing_indicators}")
            self.key_indicators = [col for col in self.key_indicators if col in self.data.columns]
        
        print(f"实际可用的关键技术指标数量：{len(self.key_indicators)}")
        
        # 数据质量检查
        print("\n数据质量检查：")
        print(f"目标列 '{self.target_column}' 缺失值数量：{self.data[self.target_column].isnull().sum()}")
        
        # 处理目标列缺失值
        initial_rows = len(self.data)
        self.data = self.data.dropna(subset=[self.target_column])
        print(f"删除目标列缺失值后剩余 {len(self.data)} 行数据")
        
        # 检查技术指标列的缺失值情况
        indicator_missing = self.data[self.key_indicators].isnull().sum()
        if indicator_missing.sum() > 0:
            print("关键技术指标列缺失值情况：")
            for col, missing_count in indicator_missing[indicator_missing > 0].items():
                print(f"  {col}: {missing_count} 个缺失值")
        
        # 填充技术指标列的缺失值（使用中位数）
        for col in self.key_indicators:
            if self.data[col].isnull().sum() > 0:
                median_val = self.data[col].median()
                self.data[col].fillna(median_val, inplace=True)
                print(f"  已用中位数 {median_val:.4f} 填充 {col} 的缺失值")
        
        # 显示目标列的基本统计信息
        print(f"\n目标列 '{self.target_column}' 基本统计：")
        print(self.data[self.target_column].describe())
        
        print(f"目标列大于0的比例：{(self.data[self.target_column] > 0).mean():.4f}")
        print("数据预处理完成！")
        
    def calculate_thresholds(self, strategy_name: str) -> Dict[str, float]:
        """
        计算指定策略的阈值
        
        Args:
            strategy_name: 策略名称
            
        Returns:
            各指标的阈值字典
        """
        percentile = self.screening_strategies[strategy_name]['percentile']
        thresholds = {}
        
        for indicator in self.key_indicators:
            threshold_value = np.percentile(self.data[indicator], percentile)
            thresholds[indicator] = threshold_value
            
        return thresholds
    
    def apply_screening_strategy(self, strategy_name: str) -> pd.DataFrame:
        """
        应用筛选策略
        
        Args:
            strategy_name: 策略名称
            
        Returns:
            筛选后的数据
        """
        thresholds = self.calculate_thresholds(strategy_name)
        
        # 应用所有指标的筛选条件（AND逻辑）
        mask = pd.Series([True] * len(self.data), index=self.data.index)
        
        for indicator, threshold in thresholds.items():
            mask = mask & (self.data[indicator] >= threshold)
        
        filtered_data = self.data[mask]
        
        return filtered_data, thresholds
    
    def calculate_statistics(self, filtered_data: pd.DataFrame) -> Dict[str, float]:
        """
        计算统计指标
        
        Args:
            filtered_data: 筛选后的数据
            
        Returns:
            统计结果字典
        """
        target_values = filtered_data[self.target_column]
        
        if len(target_values) == 0:
            return {
                'sample_count': 0,
                'sample_ratio': 0.0,
                'win_rate_gt0': 0.0,
                'win_rate_gt1': 0.0,
                'win_rate_gt3': 0.0,
                'mean_return': 0.0,
                'median_return': 0.0,
                'std_return': 0.0,
                'max_return': 0.0,
                'min_return': 0.0
            }
        
        stats = {
            'sample_count': len(target_values),
            'sample_ratio': len(target_values) / len(self.data),
            'win_rate_gt0': (target_values > 0).mean(),
            'win_rate_gt1': (target_values > 1).mean(),
            'win_rate_gt3': (target_values > 3).mean(),
            'mean_return': target_values.mean(),
            'median_return': target_values.median(),
            'std_return': target_values.std(),
            'max_return': target_values.max(),
            'min_return': target_values.min()
        }
        
        return stats
    
    def analyze_all_strategies(self) -> None:
        """分析所有筛选策略"""
        print("\n开始分析各种筛选策略...")
        
        for strategy_name in self.screening_strategies.keys():
            print(f"\n正在分析：{strategy_name}")
            
            # 应用筛选策略
            filtered_data, thresholds = self.apply_screening_strategy(strategy_name)
            
            # 计算统计指标
            stats = self.calculate_statistics(filtered_data)
            
            # 存储结果
            self.analysis_results[strategy_name] = {
                'thresholds': thresholds,
                'statistics': stats
            }
            
            print(f"  筛选后样本数：{stats['sample_count']}")
            print(f"  样本占比：{stats['sample_ratio']:.2%}")
            print(f"  平均收益：{stats['mean_return']:.4f}")
            print(f"  胜率(>0)：{stats['win_rate_gt0']:.4f}")
        
        print("\n所有策略分析完成！")
    
    def generate_comparison_table(self) -> pd.DataFrame:
        """生成对比表格"""
        print("\n生成对比表格...")
        
        comparison_data = []
        
        for strategy_name, results in self.analysis_results.items():
            stats = results['statistics']
            thresholds = results['thresholds']
            
            # 构建阈值信息字符串
            threshold_info = "; ".join([f"{k}: {v:.4f}" for k, v in thresholds.items()])
            
            row = {
                '筛选策略': strategy_name,
                '策略描述': self.screening_strategies[strategy_name]['description'],
                '筛选后样本数': stats['sample_count'],
                '样本占比': f"{stats['sample_ratio']:.2%}",
                '胜率(>0)': f"{stats['win_rate_gt0']:.4f}",
                '胜率(>1)': f"{stats['win_rate_gt1']:.4f}",
                '胜率(>3)': f"{stats['win_rate_gt3']:.4f}",
                '平均收益': f"{stats['mean_return']:.4f}",
                '中位数收益': f"{stats['median_return']:.4f}",
                '收益标准差': f"{stats['std_return']:.4f}",
                '最大收益': f"{stats['max_return']:.4f}",
                '最小收益': f"{stats['min_return']:.4f}",
                '具体阈值': threshold_info
            }
            
            comparison_data.append(row)
        
        comparison_df = pd.DataFrame(comparison_data)
        return comparison_df

    def display_results(self, comparison_df: pd.DataFrame) -> None:
        """显示分析结果"""
        print("\n" + "="*120)
        print("股票筛选策略对比分析结果")
        print("="*120)

        # 显示基准数据
        baseline_stats = {
            'total_samples': len(self.data),
            'baseline_mean': self.data[self.target_column].mean(),
            'baseline_win_rate': (self.data[self.target_column] > 0).mean(),
            'baseline_std': self.data[self.target_column].std()
        }

        print(f"\n基准数据（全样本）：")
        print(f"- 总样本数：{baseline_stats['total_samples']}")
        print(f"- 平均收益：{baseline_stats['baseline_mean']:.4f}")
        print(f"- 胜率(>0)：{baseline_stats['baseline_win_rate']:.4f}")
        print(f"- 收益标准差：{baseline_stats['baseline_std']:.4f}")

        # 显示对比表格
        print(f"\n筛选策略对比表格：")
        print("-" * 120)

        # 格式化显示主要指标
        print(f"{'策略名称':<12} {'样本数':<8} {'样本占比':<10} {'胜率(>0)':<10} {'胜率(>1)':<10} {'胜率(>3)':<10} {'平均收益':<10} {'收益改进':<10}")
        print("-" * 120)

        for _, row in comparison_df.iterrows():
            strategy_name = row['筛选策略']
            sample_count = row['筛选后样本数']
            sample_ratio = row['样本占比']
            win_rate_0 = row['胜率(>0)']
            win_rate_1 = row['胜率(>1)']
            win_rate_3 = row['胜率(>3)']
            mean_return = float(row['平均收益'])

            # 计算收益改进
            improvement = ((mean_return - baseline_stats['baseline_mean']) / baseline_stats['baseline_mean'] * 100)

            print(f"{strategy_name:<12} {sample_count:<8} {sample_ratio:<10} {win_rate_0:<10} {win_rate_1:<10} {win_rate_3:<10} {mean_return:<10.4f} {improvement:<10.1f}%")

        print("-" * 120)

        # 显示详细阈值信息
        print(f"\n各策略具体阈值：")
        for strategy_name, results in self.analysis_results.items():
            print(f"\n{strategy_name}（{self.screening_strategies[strategy_name]['description']}）：")
            for indicator, threshold in results['thresholds'].items():
                print(f"  {indicator}: {threshold:.4f}")

    def save_results(self, comparison_df: pd.DataFrame, output_file: str = "筛选策略对比分析_2024.csv") -> None:
        """保存结果到CSV文件"""
        try:
            comparison_df.to_csv(output_file, index=False, encoding='utf-8-sig')
            print(f"\n分析结果已保存到：{output_file}")
        except Exception as e:
            print(f"保存文件时出错：{e}")

    def run_complete_analysis(self) -> pd.DataFrame:
        """运行完整的分析流程"""
        print("开始股票筛选策略分析...")

        try:
            # 1. 数据加载和预处理
            self.load_and_preprocess_data()

            # 2. 分析所有策略
            self.analyze_all_strategies()

            # 3. 生成对比表格
            comparison_df = self.generate_comparison_table()

            # 4. 显示结果
            self.display_results(comparison_df)

            # 5. 保存结果
            self.save_results(comparison_df)

            return comparison_df

        except Exception as e:
            print(f"分析过程中出现错误：{e}")
            import traceback
            traceback.print_exc()
            return None


def predict_stock_screening(data: pd.DataFrame, return_details: bool = False):
    """
    股票筛选预测函数

    基于已验证的5个最佳技术指标进行股票筛选预测，使用85%阈值的最佳策略。
    该函数可以对任意股票数据进行筛选，预测哪些股票符合投资条件。

    Args:
        data (pd.DataFrame): 包含股票数据和技术指标列的DataFrame
        return_details (bool, optional): 是否返回详细的筛选信息。默认为False。
            - False: 返回布尔值列表
            - True: 返回包含详细信息的字典

    Returns:
        如果 return_details=False:
            list: 布尔值列表，表示每只股票是否通过筛选

        如果 return_details=True:
            dict: 包含以下键值的字典:
                - 'screening_results': 布尔值列表，筛选结果
                - 'selected_stocks': DataFrame，通过筛选的股票数据
                - 'total_stocks': int，总股票数量
                - 'selected_count': int，通过筛选的股票数量
                - 'selection_rate': float，筛选通过率
                - 'screening_conditions': dict，使用的筛选条件
                - 'missing_indicators': list，缺失的技术指标列
                - 'expected_performance': dict，基于历史数据的预期表现

    Raises:
        ValueError: 当输入数据为空或缺少关键技术指标列时
        TypeError: 当输入数据不是pandas DataFrame时

    Examples:
        >>> # 简单使用 - 只获取筛选结果
        >>> import pandas as pd
        >>> stock_data = pd.read_csv('stock_data.csv')
        >>> results = predict_stock_screening(stock_data)
        >>> print(f"通过筛选的股票数量: {sum(results)}")

        >>> # 详细使用 - 获取完整信息
        >>> detailed_results = predict_stock_screening(stock_data, return_details=True)
        >>> print(f"筛选通过率: {detailed_results['selection_rate']:.2%}")
        >>> selected_stocks = detailed_results['selected_stocks']
        >>> print(f"筛选出的股票代码: {selected_stocks['secID'].tolist()}")

        >>> # 处理筛选结果
        >>> if detailed_results['selected_count'] > 0:
        >>>     print("推荐投资组合:")
        >>>     for idx, row in detailed_results['selected_stocks'].iterrows():
        >>>         print(f"  {row['secID']}: 预期收益 {row.get('3H_2buy', 'N/A')}%")

    Note:
        筛选条件基于85%阈值的最佳策略，历史验证结果显示:
        - 平均收益率: 6.25%
        - 胜率: 100%
        - 收益改进: +88.4%

        使用的5个技术指标:
        1. i1_MA20_ratio: 20日移动平均比率
        2. i1_最高_收盘比例: 价格位置指标
        3. i1_EMA10_ratio: 10日指数移动平均比率
        4. i1_EMA5_ratio: 5日指数移动平均比率
        5. i1_EMA20_ratio: 20日指数移动平均比率
    """

    # 输入验证
    if not isinstance(data, pd.DataFrame):
        raise TypeError("输入数据必须是pandas DataFrame格式")

    if data.empty:
        raise ValueError("输入数据不能为空")

    # 基于85%阈值的最佳筛选条件
    screening_conditions = {
        'i1_MA20_ratio': 1.0049,
        'i1_最高_收盘比例': 0.0583,
        'i1_EMA10_ratio': 1.0136,
        'i1_EMA5_ratio': 1.0268,
        'i1_EMA20_ratio': 1.0070
    }

    # 检查必需的技术指标列
    required_columns = list(screening_conditions.keys())
    missing_columns = [col for col in required_columns if col not in data.columns]

    if missing_columns:
        if return_details:
            # 返回详细错误信息
            return {
                'screening_results': [False] * len(data),
                'selected_stocks': pd.DataFrame(),
                'total_stocks': len(data),
                'selected_count': 0,
                'selection_rate': 0.0,
                'screening_conditions': screening_conditions,
                'missing_indicators': missing_columns,
                'error': f"缺少必需的技术指标列: {missing_columns}",
                'expected_performance': None
            }
        else:
            raise ValueError(f"缺少必需的技术指标列: {missing_columns}")

    # 处理缺失值
    data_clean = data.copy()
    for col in required_columns:
        if data_clean[col].isnull().any():
            # 使用中位数填充缺失值
            median_val = data_clean[col].median()
            data_clean[col].fillna(median_val, inplace=True)

    # 应用筛选条件
    screening_mask = pd.Series([True] * len(data_clean), index=data_clean.index)

    for indicator, threshold in screening_conditions.items():
        condition = data_clean[indicator] >= threshold
        screening_mask = screening_mask & condition

    # 获取筛选结果
    screening_results = screening_mask.tolist()
    selected_stocks = data_clean[screening_mask].copy()

    # 基于历史验证的预期表现
    expected_performance = {
        'average_return': 6.25,
        'win_rate': 100.0,
        'improvement_rate': 88.4,
        'risk_return_ratio': 1.44,
        'historical_sample_count': 8,
        'baseline_return': 3.32
    }

    if return_details:
        # 返回详细信息
        return {
            'screening_results': screening_results,
            'selected_stocks': selected_stocks,
            'total_stocks': len(data),
            'selected_count': len(selected_stocks),
            'selection_rate': len(selected_stocks) / len(data),
            'screening_conditions': screening_conditions,
            'missing_indicators': [],
            'expected_performance': expected_performance,
            'screening_summary': {
                'conditions_used': len(screening_conditions),
                'stocks_passed_all': len(selected_stocks),
                'individual_pass_rates': {
                    indicator: (data_clean[indicator] >= threshold).sum()
                    for indicator, threshold in screening_conditions.items()
                }
            }
        }
    else:
        # 返回简单的布尔值列表
        return screening_results


def main():
    """主程序入口"""
    # CSV文件路径
    csv_file_path = "15di_fantan_2024110120250621.csv"

    print("股票技术指标筛选策略分析程序")
    print("="*50)
    print(f"分析文件：{csv_file_path}")
    print("基于前期分析结果的最佳技术指标进行筛选策略对比")
    print()

    try:
        # 创建分析器实例
        analyzer = StockScreeningAnalyzer(csv_file_path)

        # 运行完整分析
        results = analyzer.run_complete_analysis()

        if results is not None:
            print(f"\n分析成功完成！")
            print(f"使用的关键技术指标：{len(analyzer.key_indicators)} 个")
            print(f"分析的筛选策略：{len(analyzer.screening_strategies)} 种")

            # 显示最佳策略推荐
            if len(results) > 0:
                # 计算综合评分（收益权重0.7，胜率权重0.3）
                best_strategy = None
                best_score = -float('inf')

                for _, row in results.iterrows():
                    mean_return = float(row['平均收益'])
                    win_rate = float(row['胜率(>0)'])
                    composite_score = 0.7 * mean_return + 0.3 * win_rate

                    if composite_score > best_score:
                        best_score = composite_score
                        best_strategy = row['筛选策略']

                print(f"\n推荐策略：{best_strategy}")
                print(f"综合评分：{best_score:.4f}")

    except FileNotFoundError:
        print(f"错误：找不到文件 '{csv_file_path}'")
        print("请确保CSV文件路径正确")
    except Exception as e:
        print(f"程序执行出现错误：{e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
